#!/usr/bin/env ruby
# Test script to verify both admin and user approval emails are sent

puts "Testing user approval email flow..."
puts "=" * 50

# Find a user that needs approval
user = User.where(approved: false).last

unless user
  puts "Creating a test user that needs approval..."
  user = User.create!(
    email: "test_approval_#{SecureRandom.hex(4)}@example.com",
    password: "password123",
    confirmed_at: Time.current,
    approved: false
  )

  # Create profile
  UserProfile.create!(
    user: user,
    first_name: "Test",
    last_name: "User"
  )
end

puts "User: #{user.email} (ID: #{user.id})"
puts "Approved: #{user.approved}"
puts ""

# Clear any existing jobs
GoodJob::Job.where(concurrency_key: 'resend_api_rate_limit').where(finished_at: nil).destroy_all
puts "Cleared existing rate-limited jobs"
puts ""

# Trigger the approval request flow (simulating what happens in UserProfilesController#update)
puts "Enqueueing admin notification..."
RateLimitedMailDeliveryJob.perform_later(
  "NotificationMailer",
  "user_approval_request_notification",
  "deliver_now",
  { args: [user] }
)

puts "Enqueueing user notification job..."
UserNotificationJob.perform_later(user)

# Check enqueued jobs
sleep 0.5 # Give jobs time to enqueue
enqueued = GoodJob::Job.where(finished_at: nil).count
puts ""
puts "Jobs enqueued: #{enqueued}"

# Check specific jobs
rate_limited = GoodJob::Job.where(concurrency_key: 'resend_api_rate_limit').where(finished_at: nil)
puts "Rate-limited jobs queued: #{rate_limited.count}"

rate_limited.each do |job|
  puts "  - #{job.job_class} (#{job.id[0..7]}...)"
end

puts ""
puts "✅ Test complete! Check worker logs to verify both emails are sent."
puts "You should see:"
puts "1. Admin approval request email sent immediately"
puts "2. User pending approval email sent after 2 second delay"