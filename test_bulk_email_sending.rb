#!/usr/bin/env ruby
# Test script to verify bulk email sending works with updated rate limiting

puts "=" * 60
puts "BULK EMAIL SENDING TEST"
puts "=" * 60
puts ""

# Clear any existing jobs
GoodJob::Job.where(concurrency_key: 'resend_api_rate_limit').where(finished_at: nil).destroy_all
puts "✓ Cleared existing rate-limited jobs"

# Test with multiple users (simulating digest emails)
test_user_count = 20
puts "\n📧 Creating #{test_user_count} test emails (simulating digest job)"
puts "-" * 40

# Track job IDs for verification
job_ids = []

test_user_count.times do |i|
  # Create a test user for each email
  user = User.find_or_create_by(email: "bulk_test_#{i}@example.com") do |u|
    u.password = "password123"
    u.confirmed_at = Time.current
    u.approved = true
  end

  # Ensure user has a profile
  unless user.user_profile
    UserProfile.create!(
      user: user,
      first_name: "Test#{i}",
      last_name: "User"
    )
  end

  # Enqueue a job directly (simulating what the digest job does)
  job = RateLimitedMailDeliveryJob.perform_later(
    "NotificationMailer",
    "daily_new_projects_digest",
    "deliver_now",
    { args: [user] }
  )

  job_ids << job.job_id if job
  print "."
end

puts "\n"
puts "✓ Enqueued #{test_user_count} email jobs"

# Check job queue status
sleep 0.5  # Give time for database writes
queued_jobs = GoodJob::Job.where(concurrency_key: 'resend_api_rate_limit', finished_at: nil)
puts "\n📊 Queue Status:"
puts "-" * 40
puts "Total jobs with rate limit key: #{queued_jobs.count}"
puts "Jobs in queue: #{queued_jobs.where(performed_at: nil).count}"
puts "Jobs performing: #{queued_jobs.where.not(performed_at: nil).count}"

# Check if all our jobs were enqueued successfully
successful_enqueues = GoodJob::Job.where(active_job_id: job_ids).count
puts "\n✅ Success Rate:"
puts "-" * 40
puts "Jobs requested: #{test_user_count}"
puts "Jobs enqueued: #{successful_enqueues}"
puts "Success rate: #{(successful_enqueues.to_f / test_user_count * 100).round(1)}%"

if successful_enqueues == test_user_count
  puts "\n🎉 SUCCESS: All #{test_user_count} jobs were enqueued!"
  puts "With perform_throttle, they will be sent at 1 email per 1.5 seconds"
  puts "Estimated time to send all: #{(test_user_count * 1.5).round} seconds"
else
  puts "\n⚠️  WARNING: Only #{successful_enqueues} of #{test_user_count} jobs were enqueued"
  puts "This indicates a configuration issue with enqueue limits"
end

puts "\n" + "=" * 60
puts "TEST COMPLETE"
puts "=" * 60
puts "\nNote: To process these jobs, ensure your worker is running:"
puts "  bundle exec good_job start"
puts "\nOr in development, jobs will process if async mode is enabled"