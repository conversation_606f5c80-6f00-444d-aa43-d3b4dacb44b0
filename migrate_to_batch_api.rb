#!/usr/bin/env ruby
# ABOUTME: Migration script to safely switch from individual email job to Batch API
# ABOUTME: Ensures smooth transition and validates configuration before switching

puts "=" * 60
puts "MIGRATION TO BATCH API"
puts "=" * 60
puts ""

puts "This script will migrate the weekly digest from individual"
puts "email sending to Batch API for improved performance."
puts ""

# Step 1: Check current configuration
puts "📋 Current Configuration Check:"
puts "-" * 40

# Check if old job exists
old_job_exists = defined?(WeeklyNewProjectsDigestJob)
new_job_exists = defined?(WeeklyNewProjectsDigestJobV2)
batch_job_exists = defined?(BatchEmailDeliveryJob)

puts "✓ Old job (WeeklyNewProjectsDigestJob): #{old_job_exists ? 'Found' : 'Not found'}"
puts "✓ New job (WeeklyNewProjectsDigestJobV2): #{new_job_exists ? 'Found' : 'Not found'}"
puts "✓ Batch job (BatchEmailDeliveryJob): #{batch_job_exists ? 'Found' : 'Not found'}"

if !new_job_exists || !batch_job_exists
  puts ""
  puts "❌ ERROR: Required jobs not found!"
  puts "Please ensure both WeeklyNewProjectsDigestJobV2 and BatchEmailDeliveryJob exist."
  exit 1
end

# Step 2: Check GoodJob configuration
puts ""
puts "📅 Checking CRON configuration:"
puts "-" * 40

config_file = File.read('config/initializers/good_job.rb')
has_old_cron = config_file.include?('WeeklyNewProjectsDigestJob')
has_new_cron = config_file.include?('WeeklyNewProjectsDigestJobV2')

if has_old_cron && !has_new_cron
  puts "⚠️  Old CRON job found, needs to be updated"
elsif has_new_cron
  puts "✓ New CRON job already configured"
elsif !has_old_cron && !has_new_cron
  puts "⚠️  No weekly digest CRON job found"
end

# Step 3: Check Resend API key
puts ""
puts "🔑 Checking Resend configuration:"
puts "-" * 40

resend_key = ENV['RESEND_API_KEY'] || Rails.application.credentials.dig(:resend, :api_key)
if resend_key.nil? || resend_key.empty?
  puts "❌ ERROR: RESEND_API_KEY not configured"
  exit 1
else
  puts "✓ Resend API key configured"
end

# Step 4: Test Batch API availability
puts ""
puts "🔌 Testing Batch API availability:"
puts "-" * 40

begin
  require 'resend'
  Resend.api_key = resend_key

  # Try a tiny test batch
  test_batch = [{
    from: "<EMAIL>",
    to: "<EMAIL>",
    subject: "Migration Test",
    html: "<p>Test</p>",
    text: "Test"
  }]

  # This would send, but we'll just check if the method exists
  if Resend::Batch.respond_to?(:send)
    puts "✓ Batch API is available in Resend gem"
  else
    puts "❌ Batch API not available in current gem version"
    puts "Please update resend gem to version 0.15.0 or higher"
    exit 1
  end
rescue => e
  puts "❌ Error checking Batch API: #{e.message}"
  exit 1
end

# Step 5: Perform migration
puts ""
puts "🚀 Ready to migrate!"
puts "-" * 40
puts ""
puts "Migration will:"
puts "1. Update CRON job to use WeeklyNewProjectsDigestJobV2"
puts "2. Clear any pending old digest jobs"
puts "3. Set up new batch-based weekly digest"
puts ""

print "Do you want to proceed? (yes/no): "
response = gets.chomp.downcase

if response == 'yes'
  puts ""
  puts "Performing migration..."
  puts ""

  # Clear old jobs if any
  if defined?(GoodJob::Job)
    old_jobs = GoodJob::Job.where(job_class: 'WeeklyNewProjectsDigestJob', finished_at: nil)
    if old_jobs.any?
      count = old_jobs.count
      old_jobs.destroy_all
      puts "✓ Cleared #{count} pending old digest jobs"
    else
      puts "✓ No pending old digest jobs found"
    end
  end

  # Update CRON if needed
  if has_old_cron && !has_new_cron
    puts ""
    puts "⚠️  MANUAL STEP REQUIRED:"
    puts "-" * 40
    puts "Please update config/initializers/good_job.rb:"
    puts ""
    puts "Change:"
    puts "  class: 'WeeklyNewProjectsDigestJob'"
    puts ""
    puts "To:"
    puts "  class: 'WeeklyNewProjectsDigestJobV2'"
    puts ""
    puts "Then restart your application."
  else
    puts "✓ CRON configuration already up to date"
  end

  puts ""
  puts "✅ Migration completed successfully!"
  puts ""
  puts "Next steps:"
  puts "1. Restart your application to load new configuration"
  puts "2. Monitor the next scheduled digest (Mondays at 9 AM UTC)"
  puts "3. Check logs for batch processing confirmation"

else
  puts ""
  puts "Migration cancelled."
end

puts ""
puts "=" * 60
puts "MIGRATION SCRIPT COMPLETE"
puts "=" * 60