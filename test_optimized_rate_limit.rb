#!/usr/bin/env ruby
# Test script to verify optimized rate limiting (1.25 seconds per email)

puts "=" * 60
puts "OPTIMIZED RATE LIMIT TEST"
puts "=" * 60
puts ""
puts "Configuration:"
puts "• Rate: 1 email per 1.25 seconds"
puts "• Throughput: 48 emails/minute"
puts "• Resend limit: 120 emails/minute (we use 40%)"
puts ""

# Clear any existing jobs
GoodJob::Job.where(concurrency_key: 'resend_api_rate_limit').where(finished_at: nil).destroy_all
puts "✓ Cleared existing rate-limited jobs"

# Test different scenarios
test_scenarios = [
  { count: 10, name: "Small batch (newsletter to admins)" },
  { count: 50, name: "Medium batch (daily digest)" },
  { count: 200, name: "Large batch (weekly digest)" }
]

test_scenarios.each do |scenario|
  puts "\n" + "=" * 60
  puts "Testing: #{scenario[:name]}"
  puts "Emails to send: #{scenario[:count]}"
  puts "-" * 40

  job_ids = []
  start_time = Time.current

  scenario[:count].times do |i|
    user = User.find_or_create_by(email: "optimized_test_#{i}@example.com") do |u|
      u.password = "password123"
      u.confirmed_at = Time.current
      u.approved = true
    end

    unless user.user_profile
      UserProfile.create!(
        user: user,
        first_name: "OptTest#{i}",
        last_name: "User"
      )
    end

    job = RateLimitedMailDeliveryJob.perform_later(
      "NotificationMailer",
      "weekly_new_projects_digest",
      "deliver_now",
      { args: [user] }
    )

    job_ids << job.job_id if job
  end

  enqueue_time = Time.current - start_time

  # Check results
  sleep 0.5
  successful = GoodJob::Job.where(active_job_id: job_ids).count

  puts "✓ Enqueued #{successful}/#{scenario[:count]} jobs"
  puts "✓ Enqueue time: #{enqueue_time.round(2)} seconds"

  if successful == scenario[:count]
    estimated_send_time = scenario[:count] * 1.25
    puts "✓ Success rate: 100%"
    puts ""
    puts "📊 Performance metrics:"
    puts "• Estimated send time: #{(estimated_send_time / 60.0).round(1)} minutes"
    puts "• Emails per minute: 48"
    puts "• Safety margin from Resend limit: 60%"
  else
    puts "⚠️  ERROR: Only #{successful} of #{scenario[:count]} jobs enqueued!"
  end

  # Clear for next test
  GoodJob::Job.where(active_job_id: job_ids).destroy_all
end

puts "\n" + "=" * 60
puts "OPTIMIZATION RECOMMENDATIONS"
puts "=" * 60
puts ""
puts "Current setting (1.25s) provides:"
puts "• Good throughput: 48 emails/minute"
puts "• High safety margin: 60% below Resend limit"
puts "• Suitable for: Most production workloads"
puts ""
puts "Alternative configurations:"
puts ""
puts "1. Conservative (1.5s per email):"
puts "   • 40 emails/minute"
puts "   • 67% safety margin"
puts "   • Use when: Network is unreliable"
puts ""
puts "2. Balanced (1.25s per email) - CURRENT:"
puts "   • 48 emails/minute"
puts "   • 60% safety margin"
puts "   • Use when: Standard production"
puts ""
puts "3. Aggressive (1.0s per email):"
puts "   • 60 emails/minute"
puts "   • 50% safety margin"
puts "   • Use when: Time-critical bulk sends"
puts ""
puts "4. Maximum safe (0.75s per email):"
puts "   • 80 emails/minute"
puts "   • 33% safety margin"
puts "   • Use when: Urgent bulk sends, stable network"
puts ""
puts "⚠️  Never go below 0.5s (120/minute) - that's Resend's hard limit!"
puts "=" * 60