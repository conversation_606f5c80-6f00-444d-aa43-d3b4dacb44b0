#!/usr/bin/env ruby
# Test script to verify bulk email sending works with 100+ users

puts "=" * 60
puts "BULK EMAIL TEST - 100 USERS"
puts "=" * 60
puts ""

# Clear any existing jobs
GoodJob::Job.where(concurrency_key: 'resend_api_rate_limit').where(finished_at: nil).destroy_all
puts "✓ Cleared existing rate-limited jobs"

# Test with 100 users (realistic weekly digest scenario)
test_user_count = 100
puts "\n📧 Creating #{test_user_count} test emails"
puts "-" * 40

# Track job IDs for verification
job_ids = []
start_time = Time.current

test_user_count.times do |i|
  # Create a test user for each email
  user = User.find_or_create_by(email: "bulk_test_#{i}@example.com") do |u|
    u.password = "password123"
    u.confirmed_at = Time.current
    u.approved = true
  end

  # Ensure user has a profile
  unless user.user_profile
    UserProfile.create!(
      user: user,
      first_name: "Test#{i}",
      last_name: "User"
    )
  end

  # Enqueue a job directly
  job = RateLimitedMailDeliveryJob.perform_later(
    "NotificationMailer",
    "weekly_new_projects_digest",
    "deliver_now",
    { args: [user] }
  )

  job_ids << job.job_id if job
  print "." if (i + 1) % 10 == 0  # Print dot every 10 emails
end

enqueue_time = Time.current - start_time
puts "\n"
puts "✓ Enqueued #{test_user_count} email jobs in #{enqueue_time.round(2)} seconds"

# Check job queue status
sleep 1  # Give time for database writes
queued_jobs = GoodJob::Job.where(concurrency_key: 'resend_api_rate_limit', finished_at: nil)
puts "\n📊 Queue Status:"
puts "-" * 40
puts "Total jobs with rate limit key: #{queued_jobs.count}"
puts "Jobs in queue: #{queued_jobs.where(performed_at: nil).count}"
puts "Jobs performing: #{queued_jobs.where.not(performed_at: nil).count}"

# Check if all our jobs were enqueued successfully
successful_enqueues = GoodJob::Job.where(active_job_id: job_ids).count
puts "\n✅ Success Rate:"
puts "-" * 40
puts "Jobs requested: #{test_user_count}"
puts "Jobs enqueued: #{successful_enqueues}"
puts "Success rate: #{(successful_enqueues.to_f / test_user_count * 100).round(1)}%"

if successful_enqueues == test_user_count
  puts "\n🎉 SUCCESS: All #{test_user_count} jobs were enqueued!"
  puts "\n⏱️  Performance Estimates:"
  puts "-" * 40
  puts "Current rate: 1 email per 1.5 seconds"
  puts "Time to send all: #{(test_user_count * 1.5 / 60.0).round(1)} minutes"
  puts ""
  puts "If optimized to 1.25 seconds:"
  puts "Time to send all: #{(test_user_count * 1.25 / 60.0).round(1)} minutes"
  puts "Time saved: #{((test_user_count * 1.5 - test_user_count * 1.25) / 60.0).round(1)} minutes"
else
  puts "\n⚠️  ERROR: Only #{successful_enqueues} of #{test_user_count} jobs were enqueued"
  puts "This indicates enqueue limits are still in place!"

  # Show which jobs failed
  failed_count = test_user_count - successful_enqueues
  puts "Failed to enqueue: #{failed_count} jobs"
end

puts "\n" + "=" * 60
puts "TEST COMPLETE"
puts "=" * 60