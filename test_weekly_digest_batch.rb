#!/usr/bin/env ruby
# Test script for Weekly Digest with Batch API

puts "=" * 60
puts "WEEKLY DIGEST BATCH API TEST"
puts "=" * 60
puts ""

# Clear any existing batch jobs
GoodJob::Job.where(job_class: 'BatchEmailDeliveryJob').where(finished_at: nil).destroy_all
puts "✓ Cleared existing batch jobs"

# Create test scenario
puts "\n📊 Test Scenario:"
puts "-" * 40

# Create test users with profiles
test_users = []
puts "Creating test users..."

150.times do |i|
  user = User.find_or_create_by(email: "digest_test_#{i}@example.com") do |u|
    u.password = "password123"
    u.confirmed_at = Time.current
    u.approved = true
    u.role = :regular
  end

  # Ensure user has profile
  unless user.user_profile
    UserProfile.create!(
      user: user,
      first_name: "Digest#{i}",
      last_name: "Test",
      default_language: i.even? ? 'sk' : 'en'
    )
  end

  test_users << user
  print "." if (i + 1) % 10 == 0
end

puts "\n✓ Created #{test_users.length} test users"

# Create some test projects (would normally come from database)
puts "\nCreating test projects..."
admin = User.find_by(role: :super_boss) || User.first
test_projects = []

5.times do |i|
  project = Project.new(
    user: admin,
    summary: "Test Project #{i + 1} for Weekly Digest",
    short_description: "This is a test project created for weekly digest testing",
    project_status: true,
    approved: true,
    semi_public: i.even?, # Mix of public and network-only
    network_only: i.odd?,
    first_published_at: (i + 1).days.ago
  )

  # Skip validation for test purposes
  project.save(validate: false)
  test_projects << project
end

puts "✓ Created #{test_projects.length} test projects"

# Simulate the weekly digest job
puts "\n🚀 Simulating Weekly Digest Job (Batch API Version)"
puts "-" * 40

# This is what would happen in the actual job
eligible_users = test_users # In reality, would filter by subscription
total_users = eligible_users.count

puts "Total eligible users: #{total_users}"
puts "Batch size: 100 emails per batch"
puts "Expected batches: #{(total_users / 100.0).ceil}"

batch_number = 0
total_emails_queued = 0

# Process in batches of 100
eligible_users.each_slice(100) do |user_batch|
  batch_number += 1

  # Prepare batch emails (simplified for testing)
  batch_emails = user_batch.map do |user|
    {
      from: "Unlisters <<EMAIL>>",
      to: user.email,
      subject: "Weekly Digest - Test",
      html: "<h1>Weekly Digest</h1><p>This is a test email for #{user.email}</p>",
      text: "Weekly Digest\n\nThis is a test email for #{user.email}"
    }
  end

  # Queue the batch job
  job = BatchEmailDeliveryJob.perform_later(
    batch_emails,
    {
      job_type: 'weekly_digest_test',
      batch_number: batch_number,
      total_batches: (total_users / 100.0).ceil
    }
  )

  total_emails_queued += batch_emails.size
  puts "  Batch #{batch_number}: Queued #{batch_emails.size} emails (Job ID: #{job.job_id[0..7]}...)"
end

puts ""
puts "✅ Test Summary:"
puts "-" * 40
puts "Users processed: #{total_users}"
puts "Emails queued: #{total_emails_queued}"
puts "Batches created: #{batch_number}"
puts ""

# Check job queue
queued_batch_jobs = GoodJob::Job.where(job_class: 'BatchEmailDeliveryJob', finished_at: nil).count
puts "📋 Queue Status:"
puts "Batch jobs in queue: #{queued_batch_jobs}"

# Performance comparison
puts "\n⚡ Performance Comparison:"
puts "-" * 40
puts "Old method (individual emails):"
puts "  • API calls: #{total_users}"
puts "  • Time estimate: #{(total_users * 1.5 / 60.0).round(1)} minutes"
puts ""
puts "New method (Batch API):"
puts "  • API calls: #{batch_number}"
puts "  • Time estimate: #{(batch_number * 1.5).round(1)} seconds"
puts ""
puts "Improvement: #{((1 - batch_number.to_f / total_users) * 100).round(1)}% reduction in API calls"

puts "\n" + "=" * 60
puts "TEST COMPLETE"
puts "=" * 60
puts "\nNote: This is a dry run. Emails are queued but not actually sent."
puts "To process jobs: bundle exec good_job start"

# Clean up test data
puts "\nCleaning up test data..."
test_projects.each(&:destroy)
puts "✓ Test projects removed"