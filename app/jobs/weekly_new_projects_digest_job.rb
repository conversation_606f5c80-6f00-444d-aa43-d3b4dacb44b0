# ABOUTME: Background job that sends weekly digest emails using Batch API for better performance
# ABOUTME: Runs weekly via cron, respects project visibility rules and sends up to 100 emails per batch
class WeeklyNewProjectsDigestJob < ApplicationJob
  queue_as :default
  
  def perform
    Rails.logger.info "[WEEKLY_DIGEST] Starting weekly new projects digest job at #{Time.current}"
    
    # Calculate the reporting period (last 7 days)
    period_start = 7.days.ago
    period_end = Time.current
    
    # Get all new projects published within the period
    new_projects = Project.where(
      first_published_at: period_start..period_end,
      approved: true,
      project_status: true
    )
    
    # Only proceed if there are new projects
    if new_projects.any?
      Rails.logger.info "[WEEKLY_DIGEST] Found #{new_projects.count} new projects published in the last 7 days"
      # Track who should receive notification (using Set to ensure uniqueness)
      users_to_notify = Set.new
      
      # Optimize queries by processing projects in batches by type
      # This avoids N+1 queries and uses parameterized queries for security
      
      # Get all network_only and semi_public projects
      network_only_projects = new_projects.select(&:network_only?)
      semi_public_projects = new_projects.select(&:semi_public?)
      
      # Process semi_public projects in one query (most efficient)
      if semi_public_projects.any?
        all_user_ids = User.active.where(approved: true).pluck(:id)
        users_to_notify.merge(all_user_ids)
        Rails.logger.info "[WEEKLY_DIGEST] #{semi_public_projects.count} semi_public projects - adding #{all_user_ids.count} active users"
      end
      
      # Process network_only projects - use batch query to avoid N+1
      if network_only_projects.any?
        owner_ids = network_only_projects.map(&:user_id).uniq
        network_users = User.connected_to_any_of(owner_ids)
                            .active
                            .where(approved: true)
                            .pluck(:id)
        
        users_to_notify.merge(network_users)
        Rails.logger.info "[WEEKLY_DIGEST] #{network_only_projects.count} network_only projects - adding #{network_users.count} network users"
      end
      
      Rails.logger.info "[WEEKLY_DIGEST] Total unique users to notify: #{users_to_notify.size}"
      
      # Get the actual user records for those who should be notified
      # Only send to users with active subscriptions (exclude free users and super_boss)
      eligible_users = User.where(id: users_to_notify.to_a)
                           .includes(:user_profile)
                           .with_paid_subscription
      
      total_users = eligible_users.count
      Rails.logger.info "[WEEKLY_DIGEST] Sending digest to #{total_users} eligible users using Batch API"

      # Process users in batches of 100 (Resend's limit)
      batch_number = 0
      eligible_users.find_in_batches(batch_size: 100) do |user_batch|
        batch_number += 1

        # Prepare batch emails - render each user's email with proper context
        batch_emails = user_batch.map do |user|
          # Filter projects this user can see
          visible_projects = filter_visible_projects_for_user(user, new_projects)

          # Skip if no visible projects
          next if visible_projects.empty?

          # Render the email for this user
          mailer = NotificationMailer.weekly_new_projects_digest(user)
          mail = mailer.message

          {
            from: mail.from.first,
            to: mail.to.first,
            subject: mail.subject,
            html: mail.html_part&.body&.to_s || mail.body.to_s,
            text: mail.text_part&.body&.to_s
          }
        end.compact

        # Send via Batch API if there are emails to send
        if batch_emails.any?
          BatchEmailDeliveryJob.perform_later(batch_emails, {
            job_type: 'weekly_digest',
            batch_number: batch_number,
            total_batches: (total_users / 100.0).ceil
          })

          Rails.logger.info "[WEEKLY_DIGEST] Queued batch #{batch_number} with #{batch_emails.size} emails"
        end
      end

      Rails.logger.info "[WEEKLY_DIGEST] Completed queueing #{batch_number} batches for #{total_users} users"
    else
      Rails.logger.info "[WEEKLY_DIGEST] No new projects found in the last 7 days, skipping digest emails"
    end
    
    Rails.logger.info "[WEEKLY_DIGEST] Weekly digest job completed at #{Time.current}"
  end

  private

  def filter_visible_projects_for_user(user, projects)
    # Filter projects based on user's visibility rules
    projects.select do |project|
      if project.semi_public?
        true # All users can see semi_public projects
      elsif project.network_only?
        # Check if user is connected to project owner
        User.connected?(user.id, project.user_id)
      else
        false # Private projects not included in digest
      end
    end
  end
end