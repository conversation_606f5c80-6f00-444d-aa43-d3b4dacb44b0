# ABOUTME: Job for sending batch emails through Resend's Batch API with rate limiting
# ABOUTME: Handles up to 100 emails per batch, includes error handling and retry logic

class BatchEmailDeliveryJob < ApplicationJob
  include GoodJob::ActiveJobExtensions::Concurrency

  # Set queue priority for email jobs
  queue_as :mailers

  # Rate limit: 1 batch per 1.5 seconds (conservative, same as individual emails)
  # Even though we're sending 100 emails, it's still 1 API request
  good_job_control_concurrency_with(
    perform_throttle: [1, 1.5.seconds],
    key: 'resend_batch_api_rate_limit'
  )

  # Retry on Resend errors with exponential backoff
  retry_on Resend::Error, wait: :exponentially_longer, attempts: 3

  def perform(email_batch, context = {})
    Rails.logger.info "[BATCH_EMAIL] Starting batch send of #{email_batch.size} emails"
    Rails.logger.info "[BATCH_EMAIL] Context: #{context[:job_type]} - #{context[:batch_number]}" if context.present?

    # Validate batch size (Resend limit is 100)
    if email_batch.size > 100
      Rails.logger.error "[BATCH_EMAIL] Batch size #{email_batch.size} exceeds limit of 100"
      raise ArgumentError, "Batch size cannot exceed 100 emails"
    end

    # Send the batch
    response = Resend::Batch.send(email_batch)

    # Process response
    process_batch_response(response, email_batch, context)

  rescue Resend::Error => e
    Rails.logger.error "[BATCH_EMAIL] Resend API error: #{e.message}"
    handle_resend_error(e, email_batch, context)
    raise # Re-raise for retry_on to handle
  rescue => e
    Rails.logger.error "[BATCH_EMAIL] Unexpected error: #{e.class.name}: #{e.message}"
    Rails.logger.error "[BATCH_EMAIL] Backtrace: #{e.backtrace.first(3).join("\n")}"
    raise
  end

  private

  def process_batch_response(response, email_batch, context)
    # Log successful sends
    if response['data'].present?
      Rails.logger.info "[BATCH_EMAIL] Successfully sent #{response['data'].size} emails"
      response['data'].each_with_index do |result, index|
        Rails.logger.debug "[BATCH_EMAIL] Email #{index + 1} sent with ID: #{result['id']}"
      end
    end

    # Handle partial failures (permissive mode)
    if response['errors'].present?
      Rails.logger.warn "[BATCH_EMAIL] Batch had #{response['errors'].size} failures"
      handle_partial_failures(response['errors'], email_batch, context)
    end

    # Log completion
    Rails.logger.info "[BATCH_EMAIL] Batch processing complete - Success: #{response['data']&.size || 0}, Errors: #{response['errors']&.size || 0}"
  end

  def handle_partial_failures(errors, email_batch, context)
    errors.each do |error|
      failed_email = email_batch[error['index']]
      Rails.logger.error "[BATCH_EMAIL] Failed at index #{error['index']}: #{error['message']}"
      Rails.logger.error "[BATCH_EMAIL] Failed email to: #{failed_email[:to]}"

      # For digest emails, we log but don't retry individual emails
      # This avoids duplicate sends and maintains simplicity
      # If critical, could implement a retry strategy here
    end
  end

  def handle_resend_error(error, email_batch, context)
    # Log specific error details for debugging
    if error.message.include?('rate_limit')
      Rails.logger.error "[BATCH_EMAIL] Rate limit hit, will retry with backoff"
    elsif error.message.include?('invalid')
      Rails.logger.error "[BATCH_EMAIL] Invalid email data in batch"
      # Could implement validation/cleanup here
    else
      Rails.logger.error "[BATCH_EMAIL] API error: #{error.message}"
    end

    # Track failed batch for monitoring
    Rails.logger.error "[BATCH_EMAIL] Failed batch context: #{context.inspect}"
  end
end