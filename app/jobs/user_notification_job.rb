# ABOUTME: Job to send user notifications with proper delay to respect Resend rate limits
# ABOUTME: Schedules user notification with delay to avoid conflicting with admin notifications

class UserNotificationJob < ApplicationJob
  queue_as :mailers

  def perform(user)
    # Schedule the user notification with a delay to respect Resend's rate limit
    # Since admin notification goes first, we delay the user notification by 2 seconds
    # This ensures we stay below Resend's 2 emails/second limit
    RateLimitedMailDeliveryJob.set(wait: 2.seconds).perform_later(
      "NotificationMailer",
      "user_pending_approval_notification",
      "deliver_now",
      { args: [user] }
    )
  end
end