class UserProfilesController < ApplicationController
  before_action :authenticate_user! 
  before_action :set_user_profile, only: %i[ edit update ]

  # GET /user_profile
  def show
    @user_profile = UserProfile.find(params[:id])
    
    #not working fuckingactionpolicyagain
    #@can_view_contact_details = allowed_to?(:view_contact_details?, @user_profile)
    
    @is_connected = @user_profile.connected_with?(current_user.id)
    #@visible_profile_data = @user_profile.visible_attributes_for(current_user.id)
  end

  # GET /user_profile/edit
  def edit
  end

  def update
    if @user_profile.update(user_profile_params)
      # Send notifications if profile is complete and user is not yet approved
      if !current_user.approved? && @user_profile.required_fields_present?
        # Send notification to admins
        NotificationMailer.user_approval_request_notification(current_user).deliver_later
        # Send confirmation to the user that their account is pending approval
        # Use separate job to avoid rate limit conflicts
        UserNotificationJob.perform_later(current_user)
      end

      # For first time users who just completed their profile, redirect to projects (only if approved)
      if current_user.sign_in_count == 1 && @user_profile.required_fields_present? && current_user.approved?
        redirect_to projects_path,
          notice: t('user_profiles.edit.success',
          default: "User profile was successfully updated."),
          status: :see_other
      else
        # For subsequent updates, stay on the edit page
        redirect_to edit_user_profile_path(@user_profile),
          notice: t('user_profiles.edit.success',
          default: "User profile was successfully updated."),
          status: :see_other
      end
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def set_language
    new_locale = params[:locale_code].to_s.strip.to_sym
    
    I18n.locale = new_locale
    current_user.user_profile.update(default_language: new_locale.to_s)
    
    # Get the current path without locale prefix
    referer = request.referer
    if referer.present?
      begin
        uri = URI.parse(referer)
        path_without_locale = uri.path.gsub(%r{^/[a-z]{2}(/|$)}, '/')
        path_without_locale = '/' if path_without_locale.blank?
        
        # Reconstruct the URL with the new locale
        redirect_to "/#{new_locale}#{path_without_locale}"
      rescue URI::InvalidURIError
        # If there's an issue parsing the referer, fall back to root
        redirect_to root_path
      end
    else
      redirect_to root_path
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_user_profile
      @user_profile = current_user.user_profile
    end

    # Only allow a list of trusted parameters through.
    def user_profile_params
      params.require(:user_profile).permit(:first_name, :last_name, :email, :bio, :phone, :city, :country)
    end
end
