# ABOUTME: Service layer for managing Stripe subscriptions and checkout sessions
# ABOUTME: Handles subscription creation, updates, cancellations, and billing portal access
class StripeSubscriptionService
  attr_reader :user
  
  def initialize(user)
    @user = user
    ensure_stripe_customer!
  end
  
  def create_checkout_session(price_id, success_url, cancel_url, coupon_code: nil, client_reference_id: nil)
    price = Stripe::Price.retrieve(price_id)
    product = Stripe::Product.retrieve(price.product)
    
    # Build checkout session parameters
    session_params = {
      customer: user.stripe_customer_id,
      payment_method_types: ['card'],
      line_items: [{
        price: price_id,
        quantity: 1
      }],
      locale: I18n.locale.to_s,
      mode: 'subscription',
      success_url: success_url,
      cancel_url: cancel_url,
      client_reference_id: client_reference_id || user.id.to_s,
      metadata: {
        user_id: user.id,
        environment: Rails.env,
        subscription_id: client_reference_id # Also store in metadata for easier tracking
      }
    }
    
    # No trial period - users pay immediately or use coupons
    # Trial functionality removed as per business requirements
    
    # Add specific coupon if provided, otherwise allow promotion codes
    if coupon_code.present?
      # Try to find coupon by promotion code name
      coupon = find_coupon_id(coupon_code)
      if coupon
        session_params[:discounts] = [{ coupon: coupon }]
        Rails.logger.info "Applying coupon '#{coupon_code}' to checkout session"
      else
        Rails.logger.warn "Coupon '#{coupon_code}' not found, allowing promotion codes instead"
        session_params[:allow_promotion_codes] = true
      end
    else
      session_params[:allow_promotion_codes] = true
    end
    
    # Add customer update for email changes and tax ID collection
    session_params[:customer_update] = {
      address: 'auto',
      name: 'auto'  # Required for tax ID collection with existing customers
    }

    # Enable tax ID collection
    session_params[:tax_id_collection] = {
      enabled: true
    }

    Stripe::Checkout::Session.create(session_params)
  end
  
  def update_subscription(new_price_id)
    subscription = get_active_stripe_subscription
    
    raise "No active subscription found" unless subscription
    
    # Get the subscription item to update
    subscription_item = subscription.items.data.first
    
    # Update to new price
    Stripe::Subscription.update(
      subscription.id,
      items: [{
        id: subscription_item.id,
        price: new_price_id
      }],
      proration_behavior: 'create_prorations',
      metadata: {
        updated_by_user_id: user.id,
        updated_at: Time.current.to_i
      }
    )
  end
  
  def cancel_subscription(at_period_end: true, reason: nil)
    subscription = get_active_stripe_subscription
    
    raise "No active subscription found" unless subscription
    
    update_params = {
      cancel_at_period_end: at_period_end
    }
    
    # Add cancellation reason to metadata
    if reason
      update_params[:metadata] = subscription.metadata.to_h.merge(
        cancel_reason: reason,
        canceled_by_user_id: user.id,
        canceled_at: Time.current.to_i
      )
    end
    
    # Cancel immediately if requested
    if !at_period_end
      update_params[:cancel_at_period_end] = false
      Stripe::Subscription.cancel(subscription.id)
    else
      Stripe::Subscription.update(subscription.id, update_params)
    end
  end
  
  def reactivate_subscription
    subscription = get_active_stripe_subscription
    
    raise "No subscription found" unless subscription
    raise "Subscription is not scheduled for cancellation" unless subscription.cancel_at_period_end
    
    Stripe::Subscription.update(
      subscription.id,
      cancel_at_period_end: false,
      metadata: subscription.metadata.to_h.merge(
        reactivated_by_user_id: user.id,
        reactivated_at: Time.current.to_i
      )
    )
  end
  
  def create_portal_session(return_url)
    ensure_stripe_customer!
    
    configuration = ensure_portal_configuration!
    
    Stripe::BillingPortal::Session.create(
      customer: user.stripe_customer_id,
      return_url: return_url,
      locale: I18n.locale.to_s,
      configuration: configuration.id
    )
  end
  
  def get_payment_methods
    return [] unless user.stripe_customer_id
    
    Stripe::PaymentMethod.list(
      customer: user.stripe_customer_id,
      type: 'card'
    )
  end
  
  def set_default_payment_method(payment_method_id)
    ensure_stripe_customer!
    
    # Attach payment method if not already attached
    payment_method = Stripe::PaymentMethod.retrieve(payment_method_id)
    if payment_method.customer != user.stripe_customer_id
      payment_method.attach(customer: user.stripe_customer_id)
    end
    
    # Set as default
    Stripe::Customer.update(
      user.stripe_customer_id,
      invoice_settings: {
        default_payment_method: payment_method_id
      }
    )
  end
  
  def get_invoices(limit: 10)
    return [] unless user.stripe_customer_id
    
    Stripe::Invoice.list(
      customer: user.stripe_customer_id,
      limit: limit
    )
  end
  
  def get_upcoming_invoice
    return nil unless user.stripe_customer_id
    
    subscription = get_active_stripe_subscription
    return nil unless subscription
    
    Stripe::Invoice.upcoming(
      customer: user.stripe_customer_id,
      subscription: subscription.id
    )
  rescue Stripe::InvalidRequestError => e
    # No upcoming invoice available
    nil
  end
  
  def sync_subscription(stripe_subscription_id)
    return nil unless stripe_subscription_id
    
    begin
      # 1. Fetch the latest subscription data from Stripe
      stripe_sub = Stripe::Subscription.retrieve(stripe_subscription_id)
      
      # 2. Use a transaction to prevent race conditions
      Subscription.transaction do
        # Find the corresponding local record
        local_sub = user.subscriptions.find_by(stripe_subscription_id: stripe_subscription_id)
        
        # 3. If webhook hasn't created it yet, create it now
        if local_sub.nil? && stripe_sub
          # Find the plan based on stripe price ID
          plan = Plan.find_by(stripe_price_id: stripe_sub.items.data.first.price.id)
          
          if plan
            local_sub = Subscription.create_from_stripe!(user, stripe_sub, plan)
            Rails.logger.info "Created subscription from sync for user #{user.id}"
          else
            Rails.logger.error "Could not find plan for price #{stripe_sub.items.data.first.price.id}"
            return nil
          end
        elsif local_sub && stripe_sub
          # 4. Sync the data and return the updated local record
          local_sub.sync_with_stripe!(stripe_sub)
          Rails.logger.info "Synced subscription #{stripe_subscription_id} for user #{user.id}"
        end
        
        return local_sub
      end
    rescue Stripe::StripeError => e
      Rails.logger.error "Failed to sync subscription #{stripe_subscription_id}: #{e.message}"
      nil
    end
  end
  
  private
  
  # Find coupon by NAME and return its Stripe ID for checkout
  # Find coupon ID using Promotion Code API
  # Users enter customer-facing promotion codes
  def find_coupon_id(promotion_code)
    StripePromotionCodeService.get_coupon_id(promotion_code)
  end
  
  # Check if coupon provides 100% discount
  def is_full_discount_coupon?(coupon_id)
    return false unless coupon_id
    
    begin
      coupon = Stripe::Coupon.retrieve(coupon_id)
      # Check if it's a 100% discount
      coupon.percent_off == 100
    rescue Stripe::StripeError => e
      Rails.logger.error "Failed to retrieve coupon #{coupon_id}: #{e.message}"
      false
    end
  end
  
  def ensure_stripe_customer!
    # If user has a stripe_customer_id, verify it exists in Stripe
    if user.stripe_customer_id.present?
      begin
        # Try to retrieve the customer from Stripe
        Stripe::Customer.retrieve(user.stripe_customer_id)
        return # Customer exists, we're good
      rescue Stripe::InvalidRequestError => e
        # Customer doesn't exist in Stripe, clear the invalid ID
        Rails.logger.warn "Stripe customer #{user.stripe_customer_id} not found for user #{user.id}, creating new one"
        user.update!(stripe_customer_id: nil)
      end
    end
    
    # Create customer synchronously only when needed for subscription
    customer = Stripe::Customer.create(
      email: user.email,
      name: user.full_name,
      metadata: {
        user_id: user.id,
        environment: Rails.env
      }
    )
    
    user.update!(stripe_customer_id: customer.id)
    Rails.logger.info "Created Stripe customer #{customer.id} for user #{user.id}"
  end
  
  def get_active_stripe_subscription
    return nil unless user.stripe_customer_id
    
    # Get active subscription from Stripe
    subscriptions = Stripe::Subscription.list(
      customer: user.stripe_customer_id,
      status: 'all',
      limit: 1
    )
    
    # Find the first active or trialing subscription
    subscriptions.data.find { |s| ['active', 'trialing'].include?(s.status) }
  end
  
  # Trial period method removed - no longer offering trials
  # Users either pay full price immediately or use coupons for discounts
  
  def ensure_portal_configuration!
    # Get or create billing portal configuration
    configurations = Stripe::BillingPortal::Configuration.list(limit: 1)
    
    if configurations.data.any?
      configurations.data.first
    else
      create_portal_configuration!
    end
  end
  
  def create_portal_configuration!
    Stripe::BillingPortal::Configuration.create(
      features: {
        customer_update: {
          enabled: true,
          allowed_updates: ['email', 'address', 'shipping', 'phone', 'tax_id']
        },
        invoice_history: {
          enabled: true
        },
        payment_method_update: {
          enabled: true
        },
        subscription_cancel: {
          enabled: true,
          mode: 'at_period_end',
          cancellation_reason: {
            enabled: true,
            options: [
              'too_expensive',
              'missing_features',
              'switched_service',
              'unused',
              'other'
            ]
          }
        },
        subscription_pause: {
          enabled: false
        },
        subscription_update: {
          enabled: true,
          default_allowed_updates: ['price', 'quantity', 'promotion_code'],
          proration_behavior: 'create_prorations'
        }
      },
      business_profile: {
        headline: 'Manage your Unlisters subscription'
        # TODO: Add privacy and terms URLs when available
        # privacy_policy_url: "#{Rails.application.config.app_url}/privacy",
        # terms_of_service_url: "#{Rails.application.config.app_url}/terms"
      }
    )
  end
end