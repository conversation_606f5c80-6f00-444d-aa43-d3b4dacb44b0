class NotificationMailer < ApplicationMailer
  # Use our custom rate-limited delivery job for Resend API compliance
  self.delivery_job = RateLimitedMailDeliveryJ<PERSON>

  def access_request_notification(project, user)
    @project = project
    @user = user
    
    with_recipient_locale(@project.user) do
      mail(to: @project.user.email, subject: I18n.t('access_request_notification.subject', default: 'New Access Request'))
    end
  end

  def approved_access_notification(project, user, current_user)
    @project = project
    @user = user
    @current_user = current_user
    @locale = @user.user_profile.default_language || I18n.default_locale

    with_recipient_locale(@user) do
      mail(to: @user.email, subject: I18n.t('approved_access.subject', default: 'Access Request Approved'))
    end
  end

  def admin_project_notification(project, admin, current_user)
    @project = project
    @admin = admin
    @user = current_user
    
    Rails.logger.info "🔔 [EMAIL] Sending admin project notification:"
    Rails.logger.info "   - Project: #{@project.id} (#{@project.summary})"
    Rails.logger.info "   - Admin: #{@admin.email}"
    Rails.logger.info "   - User: #{@user.email}"
    
    with_recipient_locale(@admin) do
      mail(to: @admin.email, subject: I18n.t('admin_project_notification.subject', default: 'Summary Approval'))
    end
  end

  def new_project_notification(project, user)
    @project = project
    @user = user
    
    with_recipient_locale(@user) do
      mail(to: @user.email, subject: I18n.t('new_project_notification.subject', default: 'New Project'))
    end
  end

  def user_approval_request_notification(user)
    @user = user
    @user_profile = user.user_profile
    
    Rails.logger.info "🔔 [EMAIL] Sending user approval request notification:"
    Rails.logger.info "   - User: #{@user.email}"
    Rails.logger.info "   - Profile: #{@user_profile.first_name} #{@user_profile.last_name}"
    
    # Send to all super_boss users
    admin_emails = User.where(role: :super_boss).pluck(:email)
    
    if admin_emails.any?
      mail(to: admin_emails, subject: I18n.t('user_approval_request_notification.subject', default: 'New User Awaiting Approval'))
    end
  end

  def user_approval_notification(user)
    @user = user
    @user_profile = user.user_profile

    with_recipient_locale(@user) do
      mail(to: @user.email, subject: I18n.t('user_approval_notification.subject', first_name: @user_profile.first_name, default: 'Great news, your account has been approved!'))
    end
  end

  def user_pending_approval_notification(user)
    @user = user
    @user_profile = user.user_profile

    Rails.logger.info "🔔 [EMAIL] Sending user pending approval notification:"
    Rails.logger.info "   - User: #{@user.email}"
    Rails.logger.info "   - Profile: #{@user_profile.first_name} #{@user_profile.last_name}"

    with_recipient_locale(@user) do
      mail(to: @user.email, subject: I18n.t('user_pending_approval_notification.subject', default: 'Your Unlisters.com Account is Pending Approval'))
    end
  end

  def daily_new_projects_digest(user)
    @user = user
    
    with_recipient_locale(@user) do
      subject = I18n.t('daily_new_projects_digest.subject', 
                      default: "New deals available on Unlisters")
      
      mail(to: @user.email, subject: subject)
    end
  end
  
  def weekly_new_projects_digest(user)
    @user = user
    
    with_recipient_locale(@user) do
      subject = I18n.t('weekly_new_projects_digest.subject', 
                      default: "Weekly deals digest from Unlisters")
      
      mail(to: @user.email, subject: subject)
    end
  end

  private

  def with_recipient_locale(recipient)
    locale = recipient.user_profile.try(:default_language) || I18n.default_locale
    I18n.with_locale(locale) { yield }
  end
end
