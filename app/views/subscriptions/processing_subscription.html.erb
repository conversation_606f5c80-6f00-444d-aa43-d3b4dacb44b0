<style>
  .processing-container {
    max-width: 600px;
    margin: 3rem auto;
    padding: 2rem;
  }

  .processing-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 3rem 2rem;
    text-align: center;
  }

  .processing-icon-wrapper {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background-color: #e8f5e9;
    border-radius: 50%;
    margin-bottom: 2rem;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }

  .processing-icon {
    color: #4caf50;
    width: 40px;
    height: 40px;
  }

  .processing-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
  }

  .processing-message {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.6;
  }

  .processing-spinner {
    display: inline-block;
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1.5rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .processing-wait-text {
    font-size: 0.95rem;
    color: #999;
  }
</style>

<div class="processing-container">
  <div class="processing-card">
    <div class="processing-icon-wrapper">
      <%= heroicon "check-circle", variant: :solid, options: { class: "processing-icon" } %>
    </div>

    <h2 class="processing-title">
      <%= t('subscriptions.processing.title', default: 'Payment Successful!') %>
    </h2>

    <p class="processing-message">
      <%= t('subscriptions.processing.message',
            default: 'We are activating your subscription. This usually takes just a few seconds...') %>
    </p>

    <div class="processing-spinner"></div>

    <p class="processing-wait-text">
      <%= t('subscriptions.processing.wait_message',
            default: 'Please wait while we complete your subscription setup.') %>
    </p>
  </div>
</div>

<script>
  // Poll for subscription status changes
  (function() {
    let pollCount = 0;
    const maxPolls = 30; // Poll for max 30 seconds
    const subscriptionId = <%= @subscription.id %>;

    function checkSubscriptionStatus() {
      pollCount++;

      fetch('<%= check_status_subscription_path(@subscription) %>', {
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.can_access_features) {
          // Subscription is now active, redirect to subscriptions page
          window.location.href = '<%= subscriptions_path %>';
        } else if (pollCount < maxPolls) {
          // Continue polling
          setTimeout(checkSubscriptionStatus, 1000);
        } else {
          // Max polls reached, redirect anyway
          window.location.href = '<%= subscriptions_path %>';
        }
      })
      .catch(error => {
        console.error('Error checking subscription status:', error);
        if (pollCount < maxPolls) {
          setTimeout(checkSubscriptionStatus, 1000);
        } else {
          window.location.href = '<%= subscriptions_path %>';
        }
      });
    }

    // Start polling after 2 seconds
    setTimeout(checkSubscriptionStatus, 2000);
  })();
</script>