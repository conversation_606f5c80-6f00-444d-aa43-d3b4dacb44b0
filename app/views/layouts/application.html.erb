<html>
  <head>
    <title>Unlisters.com | <%= t('application.title') %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <% if Rails.env.production? %>
      <meta name="google-maps-api-key" content="<%= Rails.application.credentials.dig(:google_maps, :pro_api_key) %>">
    <% else %>
      <meta name="google-maps-api-key" content="<%= Rails.application.credentials.dig(:google_maps, :dev_api_key) %>">
    <% end %>
    
    <!-- reCAPTCHA v3 -->
    <% if Rails.application.credentials.recaptcha&.dig(:site_key).present? %>
      <meta name="recaptcha-site-key" content="<%= Rails.application.credentials.recaptcha[:site_key] %>">
      <script src="https://www.google.com/recaptcha/api.js?render=<%= Rails.application.credentials.recaptcha[:site_key] %>" async defer></script>
    <% end %>
    
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= stylesheet_link_tag "application" %>
    <%= vite_client_tag %>
    <%= vite_javascript_tag 'application' %>

    <link rel="icon" type="image/png" href="<%= asset_path('favicon.svg') %>"> 

    <% if Rails.env.production? %>
      <%= Sentry.get_trace_propagation_meta.html_safe %>
    <% end %>
  </head>

  <body>

    <div class="app-layout-with-sidebar">
      <% if user_signed_in? %>
        <!-- Sidebar -->
        <aside class="sidebar">
          <!-- Logo -->
          <div class="sidebar-header">
            <%= link_to root_path, class: "logo-container" do %>
              <%= image_tag(asset_path('logo.svg'), class:'sidebar-logo', alt: "unlisters") %>
            <% end %>
          </div>

          <!-- Navigation -->
          <nav class="sidebar-nav">
            <!-- Deals Section -->
            <div class="nav-section">
              <div class="nav-section-header">
                <%= heroicon "document-text", variant: :outline,  options: { class: "icon-24" } %>
                <%= t('application.menu.projects') %>
              </div>
              <div class="nav-section-items">
                <%= link_to t('application.menu.all_projects'), projects_path, class: ("nav-item" + (current_page?(projects_path) || current_page?(root_path) ? " active" : "")) %>
                <%= link_to show_my_projects_path, class: ("nav-item" + (current_page?(show_my_projects_path) ? " active" : "")) do %>
                  <%= t('application.menu.my_projects') %>
                  <span id="myProjectsBadge" class="mini-badge" style="display:none;"></span>
                <% end %>
              </div>
            </div>

            <!-- Wants Section -->
            <div class="nav-section">
              <div class="nav-section-header">
                <%= heroicon "magnifying-glass", variant: :outline,  options: { class: "icon-24" } %>
                <%= t('application.menu.wants') %>
              </div>
              <div class="nav-section-items">
                <%= link_to t('application.menu.all_wants'), wants_path, class: ("nav-item" + (current_page?(wants_path) ? " active" : "")) %>
                <%= link_to show_my_wants_path, class: ("nav-item" + (current_page?(show_my_wants_path) ? " active" : "")) do %>
                  <%= t('application.menu.my_wants') %>
                  <span id="myWantsBadge" class="mini-badge" style="display:none;"></span>
                <% end %>
              </div>
            </div>

            <!-- Network Section -->
            <div class="nav-section">
              <div class="nav-section-header">
                <%= heroicon "users", variant: :outline,  options: { class: "icon-24" } %>
                <%= t('application.menu.network') %>
              </div>
              <div class="nav-section-items">
                <%= link_to t('application.menu.unlisters'), network_connections_path, class: ("nav-item" + (current_page?(network_connections_path) ? " active" : "")) %>
                <%= link_to my_network_connections_path, class: ("nav-item" + (current_page?(my_network_connections_path) ? " active" : "")) do %>
                  <%= t('application.menu.my_connections') %>
                <% end %>
                <%= link_to t('application.menu.invite_users'), invite_network_connections_path, class: ("nav-item" + (current_page?(invite_network_connections_path) ? " active" : "")) %>
              </div>
            </div>

            <!-- My Profile Section -->
            <div class="nav-section">
              <div class="nav-section-header">
                <%= heroicon "cog-6-tooth", variant: :outline,  options: { class: "icon-24" } %>
                <%= t('application.menu.my_profile') %>
              </div>
              <div class="nav-section-items">
                <%= link_to edit_user_profile_path(current_user.user_profile), class: ("nav-item" + (current_page?(edit_user_profile_path(current_user.user_profile)) ? " active" : "")) do %>
                  <%= t('application.menu.profile') %>
                  <% if !current_user.user_profile.last_name? %>
                    <span class="mini-badge"></span>
                  <% end %>
                <% end %>
                <% if STRIPE_MODE == 'live' || current_user.active_tier == 'pilot' || current_user.admin? || current_user.super_admin? %>
                  <%= link_to t('application.menu.subscriptions'), subscriptions_path, class: ("nav-item" + (current_page?(subscriptions_path) ? " active" : "")) %>
                <% end %>
                <%= button_to t('common.actions.logout'), destroy_user_session_path, method: :delete, class: "nav-item logout-btn" %>
              </div>
            </div>

            <!-- Admin Section -->
            <% if current_user && (current_user.super_admin? || current_user.admin?) %>
              <div class="nav-section">
                <div class="nav-section-header admin-header">
                  ADMIN
                </div>
                <div class="nav-section-items">
                  <%= link_to "Projects Dashboard", user_admin_dashboard_path, class: "nav-item" %>
                  <%= link_to "User Management", admin_subscriptions_path, class: "nav-item" %>
                  <%#= link_to "Referral Codes", admin_referral_codes_path, class: "nav-item" %>
                </div>
              </div>
            <% end %>
          </nav>
        </aside>
      <% end %>

      <!-- Main Content -->
      <main>
        <% if user_signed_in? %>
          <!-- Header -->
          <header class="main-header">
            <div class="header-actions">
              <div class="notification">
                <%= link_to connection_requests_path do %>
                  <%= heroicon "bell", variant: :outline,  options: { class: "icon-24" } %>
                  <span id="connectionRequestsCount" class="count-badge navbar"></span>
                <% end %>
              </div>
              <div class="helpdesk-dropdown">
                <div class="helpdesk-dropdown__toggle" id="helpdeskToggle">
                  <%= heroicon "phone", variant: :outline, options: { class: "icon-24" } %>
                </div>
                <div class="helpdesk-dropdown__menu hidden" id="helpdeskMenu">
                  <div class="helpdesk-dropdown__item">
                    Helpdesk: <%= link_to "+421 910 584 228", "tel:+421910584228", class: "helpdesk-dropdown__phone" %>
                  </div>
                </div>
              </div>
              <div class="language-dropdown">
                <div class="language-dropdown__current" id="currentLanguage">
                  <%= I18n.locale.to_s.upcase %>
                </div>
                <div class="language-dropdown__menu hidden" id="languageMenu">
                  <%= link_to "EN", set_user_language_path(locale_code: :en), class: "language-dropdown__item" %>
                  <%= link_to "SK", set_user_language_path(locale_code: :sk), class: "language-dropdown__item" %>
                  <%= link_to "CS", set_user_language_path(locale_code: :cs), class: "language-dropdown__item" %>
                </div>
              </div>
              <div class="navbar__circle" id="navbarToggle">
                <span class="navbar__initials">
                  <%= current_user.user_profile.first_name.first if current_user.user_profile.first_name.present? %>
                </span>
              </div>
              
              <!-- User Menu Dropdown -->
              <ul class="navbar__menu navbar__menu--hidden" id="navbarMenu">
                <li>
                  <%= link_to t('models.user_profile.one'), edit_user_profile_path(current_user.user_profile) %>
                </li>
                <li>
                  <%= button_to t('common.actions.logout'), destroy_user_session_path, method: :delete, class: "button-outline" %>
                </li>
              </ul>
            </div>
          </header>
        <% else %>
          <!-- Public Header -->
          <header class="public-header">
            <%= link_to root_path, class: "navbar__brand" do %>
              <%= image_tag(asset_path('logo.svg'), class:'navbar__image', alt: "") %>
            <% end %>
            <div class="public-nav">
              <%= link_to "Home", public_root_path %>
              <%= link_to "Sign Up", new_user_registration_path, class: "button-outline" %>
              <%= link_to "Login", new_user_session_path, class: "button" %>
            </div>
          </header>
        <% end %>

        <!-- Content Container -->
        <div class="content-container">
          <% if notice %>
              <div class="bg-light text-c text-green p-1 mb-1">
                <%= notice %>
              </div>
          <% end %>
          
          <% if alert %>
              <div class="bg-light text-c text-red p-1">
                <%= alert %>
              </div>
          <% end %>
        
          <%= yield %>
        </div>
      </main>
    </div>


    <div id="customModal" class="dynamic-modal hidden">
      <div class="modal-content">
        <span class="close-button">&times;</span>
        <div id="modalContent"></div>
      </div>
    </div>

    <!-- Secure File Lightbox -->
    <div id="secureLightbox" class="secure-lightbox hidden">
      <div class="lightbox-backdrop"></div>
      <div class="lightbox-content">
        <div class="lightbox-header">
          <button class="lightbox-close" aria-label="Close">&times;</button>
        </div>
        <div class="lightbox-body">
          <div class="lightbox-loading">
            <div class="loading-spinner"></div>
            <p>Loading secure content...</p>
          </div>
        </div>
        <div class="lightbox-footer">
          <button class="lightbox-download" style="display: none;">
            <%= heroicon "arrow-down-circle", variant: :outline, options: { class: "icon-16" } %>
            Download
          </button>
        </div>
      </div>
    </div>
  </body>
</html>