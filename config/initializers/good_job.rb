# config/initializers/good_job.rb
Rails.application.configure do
  config.good_job.preserve_job_records = true
  config.good_job.retry_on_unhandled_error = false
  config.good_job.on_thread_error = ->(exception) { Rails.error.report(exception) }

  # Use async in development, external in production (separate worker process)
  config.good_job.execution_mode = Rails.env.development? ? :async : :external

  # Configure queues with rate limiting for email delivery
  # mailers queue limited to 1 thread to prevent race conditions and respect Resend's rate limit
  # Format: "queue1:threads;queue2:threads;*" where * means all other queues
  config.good_job.queues = 'mailers:1;default:3;*:5'
  config.good_job.max_threads = 10 # Total threads across all queues

  config.good_job.shutdown_timeout = 30 # seconds to wait for jobs to finish before shutting down

  config.good_job.enable_cron = true
  config.good_job.cron = {
    stale_uploads_cleanup: {
      cron: '0 3 * * *', # Every day at 3:00 AM UTC
      class: 'StaleUploadsCleanupJob',
      description: "Cleans up uploads that have been stuck for over 24 hours."
    },
    email_health_check: {
      cron: '0 * * * *', # Every hour
      class: 'EmailHealthCheckJob',
      description: "Monitors email delivery health and alerts on failures."
    },
    weekly_digest: {
      cron: '0 9 * * 1', # Every Monday at 9:00 AM UTC (10:00 AM CET, 11:00 AM CEST)
      class: 'WeeklyNewProjectsDigestJob',
      description: "Sends weekly digest emails to subscribed users."
    }
  }
end 