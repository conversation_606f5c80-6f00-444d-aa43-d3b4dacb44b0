#!/usr/bin/env ruby
# Real test of Resend Batch API with actual API call (dry run mode)

require 'resend'

puts "=" * 60
puts "RESEND BATCH API - REAL TEST"
puts "=" * 60
puts ""

# Configure Resend
Resend.api_key = ENV['RESEND_API_KEY'] || Rails.application.credentials.dig(:resend, :api_key)

if Resend.api_key.nil? || Resend.api_key.empty?
  puts "❌ ERROR: RESEND_API_KEY not configured"
  puts "Please set your API key in credentials or environment"
  exit 1
end

puts "✓ API Key configured"
puts ""

# Create a small test batch (using safe test addresses)
test_emails = [
  {
    from: "Unlisters <<EMAIL>>",
    to: "<EMAIL>",  # Resend's test address
    subject: "Test Batch Email 1 - #{Time.current}",
    html: "<h1>Test Email 1</h1><p>This is a test of the batch API.</p>",
    text: "Test Email 1\n\nThis is a test of the batch API."
  },
  {
    from: "Unlisters <<EMAIL>>",
    to: "<EMAIL>",  # <PERSON>sen<PERSON>'s test address
    subject: "Test Batch Email 2 - #{Time.current}",
    html: "<h1>Test Email 2</h1><p>Second email in the batch.</p>",
    text: "Test Email 2\n\nSecond email in the batch."
  },
  {
    from: "Unlisters <<EMAIL>>",
    to: "<EMAIL>",  # Resend's test address
    subject: "Test Batch Email 3 - #{Time.current}",
    html: "<h1>Test Email 3</h1><p>Third email in the batch.</p>",
    text: "Test Email 3\n\nThird email in the batch."
  }
]

puts "Prepared batch of #{test_emails.length} test emails"
puts "All emails will be sent to: <EMAIL> (safe test address)"
puts ""

print "Do you want to send this test batch? (yes/no): "
response = gets.chomp.downcase

if response == 'yes'
  puts "\nSending batch..."
  puts "-" * 40

  begin
    # Send the batch
    result = Resend::Batch.send(test_emails)

    puts "✅ Batch API call successful!"
    puts ""
    puts "Response:"
    puts JSON.pretty_generate(result)

    # Check for successful emails
    if result['data'] && result['data'].any?
      puts "\n✅ Successfully sent #{result['data'].length} emails:"
      result['data'].each_with_index do |email, index|
        puts "  #{index + 1}. Email ID: #{email['id']}"
      end
    end

    # Check for errors
    if result['errors'] && result['errors'].any?
      puts "\n⚠️  Errors encountered:"
      result['errors'].each do |error|
        puts "  - Index #{error['index']}: #{error['message']}"
      end
    end

  rescue => e
    puts "❌ Error sending batch: #{e.message}"
    puts "Error class: #{e.class}"
    puts "Backtrace: #{e.backtrace.first(3).join("\n")}"
  end
else
  puts "\nTest cancelled."
end

puts "\n" + "=" * 60
puts "BATCH API TEST COMPLETE"
puts "=" * 60