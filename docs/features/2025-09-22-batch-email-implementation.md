# ABOUTME: Documentation for Batch API implementation for weekly digest emails
# ABOUTME: Covers architecture, performance improvements, and migration steps

# Batch Email Implementation for Weekly Digest

## Overview

This document describes the implementation of Resend's Batch API for sending weekly digest emails, providing a **98.7% reduction in API calls** and significantly improved performance.

## Problem Statement

The original implementation sent individual emails through `RateLimitedMailDeliveryJob`, which:
- Made 1 API call per email recipient
- Took ~3.8 minutes to send 150 emails (at 1.5s throttle)
- Created potential bottlenecks for large user bases
- Had concurrency conflicts preventing multiple email types from being sent

## Solution: Batch API Implementation

### Key Benefits
- **98.7% reduction in API calls** (from 150 to 2 for 150 users)
- **Time reduction from 3.8 minutes to 3 seconds**
- **Scalable to thousands of users**
- **Maintains rate limit safety** (well under Resend's limits)

### Architecture

```
WeeklyNewProjectsDigestJobV2
    ↓
[Processes users in batches of 100]
    ↓
BatchEmailDeliveryJob (per batch)
    ↓
Resend::Batch.send (1 API call per 100 emails)
```

### Implementation Details

#### 1. BatchEmailDeliveryJob (`app/jobs/batch_email_delivery_job.rb`)

- Handles up to 100 emails per batch (Resend's limit)
- Includes retry logic with exponential backoff
- Rate limited to 1 batch per 1.5 seconds
- Handles partial failures gracefully
- Detailed logging for monitoring

Key features:
```ruby
good_job_control_concurrency_with(
  perform_throttle: [1, 1.5.seconds],
  key: 'resend_batch_api_rate_limit'
)
```

#### 2. WeeklyNewProjectsDigestJobV2 (`app/jobs/weekly_new_projects_digest_job_v2.rb`)

- Replaces original `WeeklyNewProjectsDigestJob`
- Processes eligible users in batches of 100
- Maintains all original business logic:
  - Project visibility rules (network_only, semi_public)
  - User subscription filtering
  - Localized email content
- Renders templates before batching
- Queues `BatchEmailDeliveryJob` for each batch

#### 3. CRON Configuration (`config/initializers/good_job.rb`)

```ruby
weekly_digest: {
  cron: '0 9 * * 1', # Every Monday at 9:00 AM UTC
  class: 'WeeklyNewProjectsDigestJobV2',
  description: "Sends weekly digest emails using Batch API"
}
```

### Constraints and Trade-offs

#### Batch API Limitations
- No file attachments (not needed for digest)
- No custom tags per email (acceptable)
- No scheduled sending (using CRON instead)
- 100 emails maximum per batch (handled automatically)

#### Design Decisions
- Keep individual email rendering for personalization
- Batch only the sending, not the content generation
- Maintain existing rate limits for safety
- Log but don't retry individual failures in batches

### Performance Comparison

| Metric | Old Method | New Method | Improvement |
|--------|------------|------------|-------------|
| API Calls (150 users) | 150 | 2 | 98.7% reduction |
| Time (150 users) | 3.8 minutes | 3 seconds | 76x faster |
| API Calls (1000 users) | 1000 | 10 | 99% reduction |
| Time (1000 users) | 25 minutes | 15 seconds | 100x faster |

### Migration Process

1. **Deploy new job classes** (already done)
2. **Run migration script**: `bundle exec rails runner migrate_to_batch_api.rb`
3. **Update CRON configuration** to use V2 job
4. **Restart application** to load new configuration
5. **Monitor next weekly digest** (Mondays at 9 AM UTC)

### Monitoring and Debugging

#### Check Batch Job Status
```ruby
GoodJob::Job.where(job_class: 'BatchEmailDeliveryJob').last(5)
```

#### View Batch Logs
```bash
grep "BATCH_EMAIL" log/development.log
grep "WEEKLY_DIGEST_V2" log/development.log
```

#### Test in Development
```bash
bundle exec rails runner test_weekly_digest_batch.rb
```

### Rollback Plan

If issues occur, revert to original job:
1. Update `config/initializers/good_job.rb` to use `WeeklyNewProjectsDigestJob`
2. Restart application
3. Original rate-limited individual sending will resume

### Future Improvements

1. **Implement for Daily Digest**: Apply same batch approach to daily digest
2. **Dynamic Batch Sizing**: Adjust batch size based on total recipients
3. **Enhanced Error Recovery**: Implement retry for individual failures
4. **Metrics Dashboard**: Add monitoring for batch performance

### Security Considerations

- API keys remain secure in Rails credentials
- No sensitive data logged
- Batch failures don't expose recipient lists
- Rate limiting prevents API abuse

## Conclusion

The Batch API implementation provides dramatic performance improvements while maintaining all existing functionality and safety measures. The solution is production-ready and can scale to thousands of users efficiently.