# Resend Batch API - Complete Analysis & Implementation Guide

## Executive Summary

**✅ The Resend Batch API IS AVAILABLE and SUPPORTED** in the Ruby gem (v0.15.0) your application is using.

## Official Documentation & Availability

### API Endpoint
- **URL**: `POST https://api.resend.com/emails/batch`
- **Ruby Method**: `Resend::Batch.send(array_of_emails)`
- **Gem Version Required**: v0.8.0+ (you have v0.15.0)

## Constraints & Limitations

### ✅ What You CAN Do
1. Send up to **100 emails per batch request**
2. Use all standard email fields:
   - `from`, `to`, `cc`, `bcc`, `reply_to`
   - `subject`, `html`, `text`
   - `headers` (custom headers)
3. Send to multiple recipients per email (up to 50)
4. Use two validation modes:
   - **Strict** (default): All emails must be valid
   - **Permissive**: Partial success allowed

### ❌ What You CANNOT Do
1. **No attachments** in batch emails
2. **No tags** for tracking/analytics
3. **No scheduled sending** (`scheduled_at` not supported)
4. **No individual tracking** per email in batch

## Pros vs Cons Analysis

### ✅ Pros
1. **99% reduction in API calls** (500 emails = 5 calls vs 500)
2. **98% faster sending** (12.5 minutes → 7.5 seconds for 500 emails)
3. **Lower database load** (5 job records vs 500)
4. **Reduced memory usage** (fewer job objects)
5. **Better for rate limiting** (5 requests vs 500)
6. **Atomic operations** (batch succeeds or fails together in strict mode)

### ❌ Cons
1. **No file attachments** - Deal breaker if you need PDFs/documents
2. **No individual tags** - Can't track opens/clicks per user
3. **Complex error handling** - Need to handle partial failures
4. **Memory spike** - Loading 100 emails in memory at once
5. **All-or-nothing in strict mode** - One bad email fails all 100
6. **Less granular monitoring** - Can't track individual email status

## Implementation Example

```ruby
# app/jobs/batch_email_delivery_job.rb
class BatchEmailDeliveryJob < ApplicationJob
  include GoodJob::ActiveJobExtensions::Concurrency

  # Rate limit: 1 batch per 1.5 seconds
  good_job_control_concurrency_with(
    perform_throttle: [1, 1.5.seconds],
    key: 'resend_batch_api_rate_limit'
  )

  def perform(email_batch)
    response = Resend::Batch.send(email_batch)

    # Handle response
    if response['errors'].present?
      handle_batch_errors(response['errors'], email_batch)
    end

    # Log success
    response['data'].each do |email_result|
      Rails.logger.info "Batch email sent: #{email_result['id']}"
    end
  rescue => e
    # Handle API errors
    Rails.logger.error "Batch email failed: #{e.message}"
    raise e
  end

  private

  def handle_batch_errors(errors, email_batch)
    errors.each do |error|
      failed_email = email_batch[error['index']]
      Rails.logger.error "Email failed at index #{error['index']}: #{error['message']}"
      # Could retry individual email here
      RateLimitedMailDeliveryJob.perform_later(
        "NotificationMailer",
        "weekly_new_projects_digest",
        "deliver_now",
        { args: [User.find_by(email: failed_email[:to])] }
      )
    end
  end
end
```

## Modified Digest Job Example

```ruby
# app/jobs/weekly_new_projects_digest_job.rb
class WeeklyNewProjectsDigestJob < ApplicationJob
  def perform
    # ... existing code to get eligible_users ...

    # Batch users into groups of 100
    eligible_users.find_in_batches(batch_size: 100) do |user_batch|
      emails = user_batch.map do |user|
        {
          from: "Unlisters <<EMAIL>>",
          to: user.email,
          subject: I18n.t('weekly_new_projects_digest.subject'),
          html: render_digest_html(user),
          text: render_digest_text(user)
        }
      end

      BatchEmailDeliveryJob.perform_later(emails)
    end
  end

  private

  def render_digest_html(user)
    # Generate HTML content for user
    ApplicationController.renderer.render(
      template: 'notification_mailer/weekly_new_projects_digest',
      layout: 'mailer',
      assigns: { user: user }
    )
  end
end
```

## Performance Impact

### Current Implementation (Individual Emails)
- **500 users**: 12.5 minutes
- **1000 users**: 25 minutes
- **5000 users**: 2 hours 5 minutes

### With Batch API
- **500 users**: 7.5 seconds (5 batches)
- **1000 users**: 15 seconds (10 batches)
- **5000 users**: 75 seconds (50 batches)

## Recommendation Decision Matrix

### ✅ USE Batch API When:
1. Sending identical or templated emails (digests)
2. No attachments needed
3. No individual tracking required
4. Sending to 50+ recipients
5. Time-sensitive bulk sends
6. You can handle partial failures

### ❌ DON'T Use Batch API When:
1. Emails have attachments (PDFs, invoices)
2. Need individual open/click tracking
3. Emails are highly personalized with different content
4. Sending fewer than 10 emails
5. Need scheduled sending
6. Cannot handle partial failures

## Final Recommendation

### For Your Use Case:

**✅ IMPLEMENT Batch API for:**
- Weekly digest emails (100+ recipients)
- Daily digest emails (50+ recipients)
- Bulk notifications without attachments

**❌ KEEP Individual API for:**
- User approval emails (1-2 recipients)
- Access request notifications (has tracking needs)
- Any emails with attachments
- Emails needing individual tags/tracking

### Implementation Priority:
1. **High Priority**: Implement for digest jobs (biggest performance gain)
2. **Medium Priority**: Create hybrid approach (batch for bulk, individual for small)
3. **Low Priority**: Optimize individual rate limiting further

## Implementation Checklist

- [ ] Create `BatchEmailDeliveryJob` class
- [ ] Add error handling for partial failures
- [ ] Update `WeeklyNewProjectsDigestJob` to use batching
- [ ] Update `DailyNewProjectsDigestJob` to use batching
- [ ] Add monitoring for batch success rates
- [ ] Test with production-like data volumes
- [ ] Add fallback to individual emails for failures
- [ ] Document batch API usage patterns
- [ ] Monitor Resend dashboard for batch performance

## Risk Mitigation

1. **Partial Failures**: Implement retry logic for failed emails in batch
2. **Memory Issues**: Monitor memory usage with large batches
3. **Rate Limits**: Keep same 1.5s throttle between batch requests
4. **Monitoring**: Add detailed logging for batch operations
5. **Rollback Plan**: Keep individual email code as fallback