# Email Batch API Implementation Proposal

## Overview
Resend offers a Batch API that can send up to 100 emails in a single request. This would dramatically improve our bulk email performance.

## Current Implementation
- **Rate**: 1 email per 1.5 seconds
- **Throughput**: 40 emails/minute
- **Time for 1000 emails**: 25 minutes

## With Batch API
- **Rate**: 1 batch (100 emails) per 1.5 seconds
- **Throughput**: 4000 emails/minute (theoretical)
- **Time for 1000 emails**: 15 seconds

## Implementation Strategy

### 1. Create a Batch Job Wrapper
```ruby
class BatchEmailDeliveryJob < ApplicationJob
  include GoodJob::ActiveJobExtensions::Concurrency

  good_job_control_concurrency_with(
    perform_throttle: [1, 1.5.seconds],
    key: 'resend_batch_api_rate_limit'
  )

  def perform(email_batch)
    # Send up to 100 emails in one API call
    Resend::Batch.send(email_batch)
  end
end
```

### 2. Modify Digest Jobs
```ruby
class WeeklyNewProjectsDigestJob < ApplicationJob
  def perform
    eligible_users = User.with_paid_subscription

    # Batch users into groups of 100
    eligible_users.find_in_batches(batch_size: 100) do |user_batch|
      emails = user_batch.map do |user|
        {
          to: user.email,
          subject: "Weekly digest",
          html: render_digest_for(user)
        }
      end

      BatchEmailDeliveryJob.perform_later(emails)
    end
  end
end
```

## Benefits
1. **98% reduction in API calls** (1000 emails = 10 API calls instead of 1000)
2. **95% reduction in sending time**
3. **Better resource utilization**
4. **Reduced database load** (fewer job records)

## Considerations
1. **Error handling**: If one email in a batch fails, need to handle partial failures
2. **Monitoring**: Need to track batch success rates
3. **Memory usage**: Preparing 100 emails in memory

## Recommendation
Implement Batch API for:
- Weekly digest emails (100+ recipients)
- Daily digest emails (50+ recipients)

Keep individual API for:
- User approval notifications (1-2 emails)
- Access request notifications (single emails)

## Timeline
Estimated implementation: 4-6 hours including testing