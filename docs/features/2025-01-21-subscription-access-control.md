# 2025-01-21 Subscription Access Control

## Overview
Implemented conditional access to subscription features based on Stripe payment mode (test vs live).

## Implementation Details

### Sidebar Menu Access (`app/views/layouts/application.html.erb`)
The subscription link in the sidebar is now conditionally displayed based on STRIPE_MODE:

- **When STRIPE_MODE = 'live'**: All logged-in users can see the subscription link
- **When STRIPE_MODE = 'test'**: Only pilot tier users, admins, and super_admins can see the link

```erb
<% if STRIPE_MODE == 'live' || current_user.active_tier == 'pilot' || current_user.admin? || current_user.super_admin? %>
  <%= link_to t('application.menu.subscriptions'), subscriptions_path, class: ("nav-item" + (current_page?(subscriptions_path) ? " active" : "")) %>
<% end %>
```

### Subscription Page Behavior (`app/views/subscriptions/index.html.erb`)
The subscription purchase button behavior:

- **When STRIPE_MODE = 'test'**:
  - Shows maintenance message to regular users
  - Button is disabled
  - Admins/pilots can still test with `?allow_test_purchase=TEST` parameter

- **When STRIPE_MODE = 'live'**:
  - No maintenance message
  - All users can purchase subscriptions
  - Real payments are processed

## Configuration

### Setting Stripe Mode
The mode is controlled by the `STRIPE_MODE` environment variable:
- Set `STRIPE_MODE=test` for test/development (default)
- Set `STRIPE_MODE=live` for production with real payments

### Required Credentials
Ensure appropriate Stripe credentials are configured in Rails credentials:
- For test mode: `stripe.test.secret_key` and `stripe.test.publishable_key`
- For live mode: `stripe.live.secret_key` and `stripe.live.publishable_key`

## Security Benefits
1. Prevents regular users from accidentally making test payments
2. Ensures production users only interact with live payment system
3. Maintains testing capability for admins and pilot users
4. Clear separation between test and production environments

## Testing
1. In test mode: Only admins/pilots see subscription link
2. In live mode: All users see subscription link
3. Maintenance message appears only in test mode for regular users
4. Payment processing works according to the configured mode