# ABOUTME: Test suite for Stripe webhook job verifying event processing logic
# ABOUTME: Tests subscription lifecycle events, payment processing, and error handling
require 'rails_helper'

RSpec.describe StripeWebhookJob, type: :job do
  let(:user) { create(:user, stripe_customer_id: 'cus_test123') }
  let(:plan) { create(:plan, stripe_price_id: 'price_test123', tier: 'standard') }
  
  describe '#perform' do
    context 'customer.subscription.created event' do
      let(:event_data) do
        {
          'id' => 'evt_test',
          'type' => 'customer.subscription.created',
          'data' => {
            'object' => {
              'id' => 'sub_test123',
              'customer' => user.stripe_customer_id,
              'status' => 'active',
              'current_period_start' => Time.now.to_i,
              'current_period_end' => 1.month.from_now.to_i,
              'items' => {
                'data' => [
                  {
                    'price' => {
                      'id' => plan.stripe_price_id,
                      'product' => 'prod_test123'
                    }
                  }
                ]
              },
              'cancel_at_period_end' => false
            }
          }
        }
      end
      
      before do
        # Mock Stripe API calls
        allow(Stripe::Product).to receive(:retrieve).and_return(
          OpenStruct.new(
            id: 'prod_test123',
            name: 'Premium Plan',
            active: true,
            metadata: { 'tier' => 'premium' }
          )
        )
      end
      
      it 'creates a new subscription record' do
        expect {
          described_class.new.perform(event_data)
        }.to change { Subscription.count }.by(1)
        
        subscription = Subscription.last
        expect(subscription.user).to eq(user)
        expect(subscription.stripe_subscription_id).to eq('sub_test123')
        expect(subscription.status).to eq('active')
        expect(subscription.plan).to eq(plan)
      end
      
      it 'handles missing user gracefully' do
        event_data['data']['object']['customer'] = 'cus_nonexistent'
        
        expect {
          described_class.new.perform(event_data)
        }.not_to raise_error
        
        expect(Subscription.count).to eq(0)
      end
    end
    
    context 'customer.subscription.updated event' do
      let!(:subscription) do
        create(:subscription,
          user: user,
          plan: plan,
          stripe_subscription_id: 'sub_test123',
          status: 'active'
        )
      end
      
      let(:event_data) do
        {
          'id' => 'evt_test',
          'type' => 'customer.subscription.updated',
          'data' => {
            'object' => {
              'id' => 'sub_test123',
              'customer' => user.stripe_customer_id,
              'status' => 'past_due',
              'current_period_start' => Time.now.to_i,
              'current_period_end' => 1.month.from_now.to_i,
              'items' => {
                'data' => [
                  {
                    'price' => {
                      'id' => plan.stripe_price_id,
                      'product' => 'prod_test123'
                    }
                  }
                ]
              },
              'cancel_at_period_end' => false
            }
          }
        }
      end
      
      it 'updates the subscription status' do
        described_class.new.perform(event_data)
        
        subscription.reload
        expect(subscription.status).to eq('past_due')
        expect(subscription.stripe_status).to eq('past_due')
      end
    end
    
    context 'customer.subscription.deleted event' do
      let!(:subscription) do
        create(:subscription,
          user: user,
          plan: plan,
          stripe_subscription_id: 'sub_test123',
          status: 'active'
        )
      end
      
      let(:event_data) do
        {
          'id' => 'evt_test',
          'type' => 'customer.subscription.deleted',
          'data' => {
            'object' => {
              'id' => 'sub_test123',
              'customer' => user.stripe_customer_id,
              'status' => 'canceled',
              'canceled_at' => Time.now.to_i
            }
          }
        }
      end
      
      it 'marks the subscription as canceled' do
        described_class.new.perform(event_data)
        
        subscription.reload
        expect(subscription.status).to eq('canceled')
        expect(subscription.canceled_at).not_to be_nil
        expect(subscription.ended_at).not_to be_nil
      end
    end
    
    context 'invoice.payment_succeeded event' do
      let!(:subscription) do
        create(:subscription,
          user: user,
          plan: plan,
          stripe_subscription_id: 'sub_test123',
          status: 'past_due'
        )
      end
      
      let(:event_data) do
        {
          'id' => 'evt_test',
          'type' => 'invoice.payment_succeeded',
          'data' => {
            'object' => {
              'id' => 'in_test123',
              'subscription' => 'sub_test123',
              'customer' => user.stripe_customer_id,
              'paid' => true
            }
          }
        }
      end
      
      it 'updates subscription status to active' do
        described_class.new.perform(event_data)
        
        subscription.reload
        expect(subscription.status).to eq('active')
      end
    end
    
    context 'invoice.payment_failed event' do
      let!(:subscription) do
        create(:subscription,
          user: user,
          plan: plan,
          stripe_subscription_id: 'sub_test123',
          status: 'active'
        )
      end
      
      let(:event_data) do
        {
          'id' => 'evt_test',
          'type' => 'invoice.payment_failed',
          'data' => {
            'object' => {
              'id' => 'in_test123',
              'subscription' => 'sub_test123',
              'customer' => user.stripe_customer_id,
              'paid' => false
            }
          }
        }
      end
      
      it 'updates subscription status to past_due' do
        described_class.new.perform(event_data)
        
        subscription.reload
        expect(subscription.status).to eq('past_due')
      end
    end
    
    context 'with nil timestamps (critical bug fix)' do
      let(:event_data) do
        {
          'id' => 'evt_test',
          'type' => 'customer.subscription.created',
          'data' => {
            'object' => {
              'id' => 'sub_nil_test',
              'customer' => user.stripe_customer_id,
              'status' => 'active',
              'current_period_start' => nil,  # Critical: nil value that was causing TypeError
              'current_period_end' => nil,    # Critical: nil value that was causing TypeError
              'trial_start' => nil,
              'trial_end' => nil,
              'items' => {
                'data' => [
                  {
                    'price' => {
                      'id' => plan.stripe_price_id,
                      'product' => 'prod_test123'
                    }
                  }
                ]
              },
              'cancel_at_period_end' => nil
            }
          }
        }
      end
      
      before do
        allow(Stripe::Product).to receive(:retrieve).and_return(
          OpenStruct.new(
            id: 'prod_test123',
            name: 'Premium Plan',
            active: true,
            metadata: { 'tier' => 'premium' }
          )
        )
      end
      
      it 'handles nil timestamps without raising TypeError' do
        expect {
          described_class.new.perform(event_data)
        }.not_to raise_error
      end
      
      it 'creates subscription with default timestamps when nil' do
        expect {
          described_class.new.perform(event_data)
        }.to change { Subscription.count }.by(1)
        
        subscription = Subscription.last
        expect(subscription.stripe_subscription_id).to eq('sub_nil_test')
        expect(subscription.current_period_start).to be_within(1.minute).of(Time.current)
        expect(subscription.current_period_end).to be_within(1.minute).of(1.month.from_now)
      end
    end
    
    context 'subscription.updated with nil timestamps' do
      let!(:subscription) do
        create(:subscription,
               user: user,
               plan: plan,
               stripe_subscription_id: 'sub_update_nil',
               current_period_start: 1.month.ago,
               current_period_end: Time.current)
      end
      
      let(:event_data) do
        {
          'id' => 'evt_test',
          'type' => 'customer.subscription.updated',
          'data' => {
            'object' => {
              'id' => 'sub_update_nil',
              'customer' => user.stripe_customer_id,
              'status' => 'active',
              'current_period_start' => nil,  # nil should keep existing value
              'current_period_end' => nil,    # nil should keep existing value
              'trial_end' => nil,
              'cancel_at_period_end' => false,
              'canceled_at' => nil,
              'items' => {
                'data' => [
                  {
                    'price' => {
                      'id' => plan.stripe_price_id
                    }
                  }
                ]
              }
            }
          }
        }
      end
      
      it 'keeps existing timestamps when update has nil' do
        original_start = subscription.current_period_start
        original_end = subscription.current_period_end
        
        described_class.new.perform(event_data)
        
        subscription.reload
        expect(subscription.current_period_start).to eq(original_start)
        expect(subscription.current_period_end).to eq(original_end)
      end
    end
    
    context 'with database transaction rollback' do
      let(:event_data) do
        {
          'id' => 'evt_test',
          'type' => 'customer.subscription.created',
          'data' => {
            'object' => {
              'id' => 'sub_test123',
              'customer' => user.stripe_customer_id,
              'status' => 'active'
            }
          }
        }
      end
      
      it 'rolls back changes on error' do
        allow_any_instance_of(Subscription).to receive(:save!).and_raise(ActiveRecord::RecordInvalid)
        
        expect {
          described_class.new.perform(event_data) rescue nil
        }.not_to change { Subscription.count }
      end
    end
  end
end