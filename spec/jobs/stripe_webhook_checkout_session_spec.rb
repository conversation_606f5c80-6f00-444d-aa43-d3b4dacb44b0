# ABOUTME: Tests for checkout.session.completed webhook handler
# ABOUTME: Ensures pre-created subscriptions are updated with Stripe IDs

require 'rails_helper'

RSpec.describe StripeWebhookJob, type: :job do
  let(:user) { create(:user) }
  let(:plan) { create(:plan, stripe_price_id: 'price_123') }
  let(:subscription) { create(:subscription, user: user, plan: plan, status: :incomplete) }
  
  describe "checkout.session.completed event" do
    let(:checkout_session_event) do
      {
        'type' => 'checkout.session.completed',
        'data' => {
          'object' => {
            'id' => 'cs_test_123',
            'object' => 'checkout.session',
            'client_reference_id' => subscription.id.to_s,
            'customer' => 'cus_test123',
            'subscription' => 'sub_test456',
            'payment_status' => 'paid',
            'status' => 'complete'
          }
        }
      }
    end
    
    it "updates the incomplete subscription with Stripe IDs" do
      expect(subscription.stripe_subscription_id).to be_nil
      expect(subscription.stripe_customer_id).to be_nil
      
      described_class.perform_now(checkout_session_event)
      
      subscription.reload
      expect(subscription.stripe_subscription_id).to eq('sub_test456')
      expect(subscription.stripe_customer_id).to eq('cus_test123')
    end
    
    it "logs successful update" do
      expect(Rails.logger).to receive(:info).with(
        "Updated subscription #{subscription.id} with Stripe IDs from checkout session"
      )
      
      described_class.perform_now(checkout_session_event)
    end
    
    context "when client_reference_id is not present" do
      before do
        checkout_session_event['data']['object']['client_reference_id'] = nil
      end
      
      it "does not process the event" do
        expect {
          described_class.perform_now(checkout_session_event)
        }.not_to change { subscription.reload.stripe_subscription_id }
      end
    end
    
    context "when subscription is not found" do
      before do
        checkout_session_event['data']['object']['client_reference_id'] = '99999'
      end
      
      it "logs missing subscription error" do
        expect(Rails.logger).to receive(:error).with(
          "No subscription found for Stripe ID: 99999"
        )
        
        described_class.perform_now(checkout_session_event)
      end
    end
  end
  
  describe "customer.subscription.created event with pre-created subscription" do
    let(:subscription_created_event) do
      {
        'type' => 'customer.subscription.created',
        'data' => {
          'object' => {
            'id' => 'sub_test456',
            'object' => 'subscription',
            'customer' => 'cus_test123',
            'status' => 'active',
            'current_period_start' => Time.current.to_i,
            'current_period_end' => 1.month.from_now.to_i,
            'cancel_at_period_end' => false,
            'items' => {
              'data' => [
                {
                  'price' => {
                    'id' => 'price_123',
                    'product' => 'prod_123',
                    'unit_amount' => 1000,
                    'recurring' => { 'interval' => 'month' }
                  }
                }
              ]
            }
          }
        }
      }
    end
    
    before do
      # Simulate that checkout.session.completed already ran
      subscription.update!(stripe_subscription_id: 'sub_test456')
      allow(Stripe::Product).to receive(:retrieve).and_return(
        double('product', 
               id: 'prod_123',
               name: 'Test Product',
               active: true,
               metadata: {})
      )
    end
    
    it "updates the existing incomplete subscription to active" do
      expect(subscription.status).to eq('incomplete')
      
      described_class.perform_now(subscription_created_event)
      
      subscription.reload
      expect(subscription.status).to eq('active')
      expect(subscription.stripe_status).to eq('active')
    end
    
    it "updates subscription periods and details" do
      described_class.perform_now(subscription_created_event)
      
      subscription.reload
      expect(subscription.current_period_start).to be_present
      expect(subscription.current_period_end).to be_present
      expect(subscription.provider_data).to be_present
    end
    
    it "logs the status update" do
      expect(Rails.logger).to receive(:info).with(
        "Updated subscription #{subscription.id} status from incomplete to active"
      )
      
      described_class.perform_now(subscription_created_event)
    end
  end
  
  describe "customer.subscription.created event fallback (no pre-created subscription)" do
    let(:new_user) { create(:user, stripe_customer_id: 'cus_fallback') }
    let!(:fallback_plan) { create(:plan, stripe_price_id: 'price_123', tier: 'standard') }
    let(:subscription_created_event) do
      {
        'type' => 'customer.subscription.created',
        'data' => {
          'object' => {
            'id' => 'sub_fallback',
            'object' => 'subscription',
            'customer' => 'cus_fallback',
            'status' => 'active',
            'current_period_start' => Time.current.to_i,
            'current_period_end' => 1.month.from_now.to_i,
            'cancel_at_period_end' => false,
            'items' => {
              'data' => [
                {
                  'price' => {
                    'id' => 'price_123',
                    'product' => 'prod_123',
                    'unit_amount' => 1000,
                    'recurring' => { 'interval' => 'month' }
                  }
                }
              ]
            }
          }
        }
      }
    end
    
    before do
      allow(Stripe::Product).to receive(:retrieve).and_return(
        double('product', 
               id: 'prod_123',
               name: 'Test Product',
               active: true,
               metadata: {})
      )
    end
    
    it "creates a new subscription using fallback flow" do
      expect {
        described_class.perform_now(subscription_created_event)
      }.to change(Subscription, :count).by(1)
      
      new_subscription = Subscription.last
      expect(new_subscription.user).to eq(new_user)
      expect(new_subscription.stripe_subscription_id).to eq('sub_fallback')
      expect(new_subscription.status).to eq('active')
    end
    
    it "logs fallback flow usage" do
      expect(Rails.logger).to receive(:info).with(
        matching(/Created new subscription .* \(fallback flow\)/)
      )
      
      described_class.perform_now(subscription_created_event)
    end
  end
end