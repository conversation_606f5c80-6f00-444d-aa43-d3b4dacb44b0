# ABOUTME: RSpec test for WeeklyNewProjectsDigestJob with Batch API implementation
# ABOUTME: Tests that emails are properly batched and sent via BatchEmailDeliveryJob

require 'rails_helper'

RSpec.describe WeeklyNewProjectsDigestJob, type: :job do
  include ActiveJob::TestHelper

  let(:admin_user) { create(:user, role: :super_boss, approved: true) }
  let!(:project) do
    project = build(:project,
      user: admin_user,
      approved: true,
      project_status: true,
      semi_public: true,
      first_published_at: 1.day.ago
    )
    project.admin_approver = admin_user
    project.is_admin_approval_action = true
    project.save(validate: false)
    project
  end

  describe '#perform with Batch API' do
    context 'with 150 eligible users' do
      let!(:plan) { create(:plan, price_cents: 1999) }
      let!(:users) do
        150.times.map do |i|
          user = create(:user,
            email: "batch_test_#{i}@example.com",
            approved: true,
            role: :regular
          )
          create(:user_profile, user: user, first_name: "Test#{i}", last_name: "User")
          # Create active subscription for user
          create(:subscription,
            user: user,
            plan: plan,
            status: 'active',
            current_period_end: 30.days.from_now
          )
          user
        end
      end

      it 'queues emails in batches of 100' do
        expect(BatchEmailDeliveryJob).to receive(:perform_later).exactly(2).times do |emails, context|
          expect(emails).to be_an(Array)
          expect(emails.size).to be <= 100
          expect(context[:job_type]).to eq('weekly_digest')
        end

        described_class.new.perform
      end

      it 'creates 2 batch jobs for 150 users' do
        expect {
          described_class.new.perform
        }.to change {
          enqueued_jobs.select { |j| j[:job] == BatchEmailDeliveryJob }.count
        }.by(2)
      end

      it 'includes correct email structure in batches' do
        allow(BatchEmailDeliveryJob).to receive(:perform_later) do |emails, _context|
          emails.each do |email|
            expect(email).to have_key(:from)
            expect(email).to have_key(:to)
            expect(email).to have_key(:subject)
            expect(email).to have_key(:html)
            expect(email).to have_key(:text)
          end
        end

        described_class.new.perform
      end
    end

    context 'with no new projects' do
      before { project.update_column(:first_published_at, 8.days.ago) }

      it 'does not queue any batch jobs' do
        expect(BatchEmailDeliveryJob).not_to receive(:perform_later)
        described_class.new.perform
      end
    end

    context 'with network_only projects' do
      let!(:network_project) do
        project = build(:project,
          user: admin_user,
          approved: true,
          project_status: true,
          network_only: true,
          semi_public: false,
          first_published_at: 1.day.ago
        )
        project.admin_approver = admin_user
        project.is_admin_approval_action = true
        project.save(validate: false)
        project
      end

      let!(:connected_user) { create(:user, approved: true, role: :regular) }
      let!(:unconnected_user) { create(:user, approved: true, role: :regular) }
      let!(:connection) do
        create(:network_connection,
          inviter: admin_user,
          invitee: connected_user,
          is_accepted: true
        )
      end

      before do
        create(:user_profile, user: connected_user)
        create(:user_profile, user: unconnected_user)
      end

      it 'only sends to connected users for network_only projects' do
        allow(BatchEmailDeliveryJob).to receive(:perform_later) do |emails, _context|
          email_recipients = emails.map { |e| e[:to] }
          expect(email_recipients).to include(connected_user.email)
          expect(email_recipients).not_to include(unconnected_user.email)
        end

        described_class.new.perform
      end
    end
  end
end